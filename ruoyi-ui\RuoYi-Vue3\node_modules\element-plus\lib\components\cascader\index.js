'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var cascader$1 = require('./src/cascader2.js');
var cascader = require('./src/cascader.js');
require('./src/instances.js');

cascader$1["default"].install = (app) => {
  app.component(cascader$1["default"].name, cascader$1["default"]);
};
const _Cascader = cascader$1["default"];
const ElCascader = _Cascader;

exports.cascaderEmits = cascader.cascaderEmits;
exports.cascaderProps = cascader.cascaderProps;
exports.ElCascader = ElCascader;
exports["default"] = _Cascader;
//# sourceMappingURL=index.js.map
