'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./src/index.js');
var types = require('./src/types.js');
var config = require('./src/config.js');
require('./src/instance.js');

index["default"].install = (app) => {
  app.component(index["default"].name, index["default"]);
};
const _CascaderPanel = index["default"];
const ElCascaderPanel = _CascaderPanel;

exports.CASCADER_PANEL_INJECTION_KEY = types.CASCADER_PANEL_INJECTION_KEY;
exports.CommonProps = config.CommonProps;
exports.DefaultProps = config.DefaultProps;
exports.useCascaderConfig = config.useCascaderConfig;
exports.ElCascaderPanel = ElCascaderPanel;
exports["default"] = _CascaderPanel;
//# sourceMappingURL=index.js.map
