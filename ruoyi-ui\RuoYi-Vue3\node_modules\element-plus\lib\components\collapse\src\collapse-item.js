'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../../utils/index.js');
var runtime = require('../../../utils/vue/props/runtime.js');
var rand = require('../../../utils/rand.js');

const collapseItemProps = runtime.buildProps({
  title: {
    type: String,
    default: ""
  },
  name: {
    type: runtime.definePropType([String, Number]),
    default: () => rand.generateId()
  },
  disabled: Boolean
});

exports.collapseItemProps = collapseItemProps;
//# sourceMappingURL=collapse-item.js.map
